package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.entity.Organisation;
import com.enttribe.emailagent.service.OrganistationService;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

/**
 * The type Organistation rest.
 *  <AUTHOR>
 */
@RestController("OrganistationRest")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/organization")
@Slf4j
public class OrganistationRest {

    @Autowired
    private OrganistationService service;


    @PostMapping("create")
    /* @Operation(summary = "Create Organisation", tags = "Organisation", description = "Creates a new organisation using the provided details.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organisation successfully created."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while creating the organisation.")
    }) */
    public Organisation create(@RequestBody Organisation organisation) {
        return service.create(organisation);
    }

    @PostMapping("update/{id}")
    /* @Operation(summary = "Update Organisation", tags = "Organisation", description = "Updates the details of an existing organisation based on the provided ID and details.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organisation successfully updated."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the organisation.")
    }) */
    public Organisation update(@PathVariable Integer id, @RequestBody Organisation organisation) {
        return service.update(id, organisation);
    }

    @PostMapping("updateStatus/{id}")
    /* @Operation(summary = "Update Organisation Status", tags = "Organisation", description = "Updates the status (active or inactive) of the organisation based on the provided ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organisation status successfully updated."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the organisation status.")
    }) */
    public Organisation updateStatus(@PathVariable Integer id, @RequestParam boolean active) {
        return service.updateStatus(id, active);
    }

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "organizationByFilter")
    /* @Operation(summary = "Get Organisations by Filter", tags = "Organisation", description = "Fetches a list of organisations based on the provided filter criteria, with optional limit values.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organisations successfully fetched based on the filter."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching organisations.")
    }) */
    List<Organisation> organisationByFilter(
                    /* @Parameter(name = "Minimum number of records required") */ @RequestParam(required = false, name = "llimit") Integer llimit,
                    /* @Parameter(name = "Maximum number of records required") */ @RequestParam(required = false, name = "ulimit") Integer ulimit,
                    /* @Parameter(name = "filtermap") */ @RequestBody Map<String, Object> filterMap){
                        log.info("Fetching organisationByFilter logs");
                        return service.organisationByFilter(llimit, ulimit, filterMap);  
                    }
         
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "organizationCountByFilter")
    /* @Operation(summary = "Get Organisation Count by Filter", tags = "Organisation", description = "Fetches the count of organisations based on the provided filter criteria.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organisation count successfully fetched based on the filter."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the organisation count.")
    }) */
    long organisationCountByFilter(
            /* @Parameter(name = "filtermap") */ @RequestBody Map<String, Object> filterMap){
                return service.organisationCountByFilter(filterMap);  
            }


    
}

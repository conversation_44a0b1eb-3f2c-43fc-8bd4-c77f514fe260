package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.DraftWrapper;
import com.enttribe.emailagent.ai.dto.draft.DraftResponse;
import com.enttribe.emailagent.ai.dto.draft.FreshDraftAIResponse;
import com.enttribe.emailagent.ai.dto.mail_summary.MailSummaryAIResponse;
import com.enttribe.emailagent.ai.service.DraftService;
import com.enttribe.emailagent.ai.utils.AIUtils;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;
import com.enttribe.emailagent.utils.CommonUtils;
import com.enttribe.emailagent.utils.EmailConstants;
import com.enttribe.emailagent.utils.FreshDraftRequest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

import static com.enttribe.emailagent.ai.utils.AIUtils.convertToJSON;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.FRESH_DRAFT;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.GENERATE_DRAFT;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.IMPROVE_DRAFT;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.IMPROVE_FRESH_DRAFT;
import static com.enttribe.emailagent.utils.EmailAgentLoggerConstants.IMPROVE_SELECTION;
import static com.google.common.base.Throwables.getStackTraceAsString;

/**
 * The type Draft rest.
 *  <AUTHOR> Pathak
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/draft")
@Slf4j
public class DraftRest {

    private final UserContextHolder userContextHolder;
    private final IMailSummaryDao mailSummaryDao;
    private final DraftService draftServiceAI;

    private static final Logger auditLog = EmailAgentLogger.getLogger(DraftRest.class);

    @Value("${pollingMode}")
    private String pollingMode;

//    /v1/generateDraft
    @PostMapping("/generateDraft")
   /* @Operation(summary = "Generate reply draft", tags = "Draft", description = "Generate a reply draft based on saved summary in database")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft generated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })*/
    public Map<String, String> generateDraft(@RequestBody DraftWrapper emailObject) {
        UserInfo userInfo = userContextHolder.getCurrentUser();
        changeDraftWrapper(emailObject);
        Map<String, String> obj = new HashMap<>();
        try {
            String objective = emailObject.getObjective();
            String internetMessageId = emailObject.getInternetMessageId();
            String messageId = emailObject.getMessageId();
            MailSummary message = mailSummaryDao.findByInternetMessageId(internetMessageId, userInfo.getId());
            if (messageId != null && !messageId.isEmpty() && message == null) {
                message = mailSummaryDao.findByMessageId(messageId, userInfo.getId());
            }
            if (message == null) {
                obj.put("error", "Something went wrong!");
                obj.put("message", "No mail summary is found for given messageId");
                return obj;
            }

            Map<String, String> auditMap = CommonUtils.getAuditMap(userInfo.getId(), message.getSubject(), message.getInternetMessageId(), GENERATE_DRAFT, objective, null, message.getMessageId(), message.getConversationId());
            String summary = message.getMessageSummary();
            MailSummaryAIResponse mailSummaryAIResponse = CommonUtils.convertJsonToObject(summary, MailSummaryAIResponse.class);
            summary = mailSummaryAIResponse.getSummaryObject().getContent();
            try {

                String previousDraft = emailObject.getPreviousDraft();
                if (emailObject.getForward() != null && emailObject.getForward()) {
                    DraftResponse draftMessage = draftServiceAI.generateForwardDraft(objective, summary, message.getFromUser(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), previousDraft, auditMap
                    );

                    obj.put(EmailConstants.RESULT, convertToJSON(draftMessage, true));
                } else {
                    DraftResponse draftMessage;
                    if (previousDraft == null || previousDraft.isBlank()) {
                        draftMessage = draftServiceAI.generateDraft(objective, summary, message.getFromUser(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), null, auditMap);
                    } else {
                        draftMessage = draftServiceAI.reGenerateDraft(objective, summary, message.getFromUser(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), previousDraft, auditMap);
                    }
                    obj.put(EmailConstants.RESULT, convertToJSON(draftMessage, true));
                }
            } catch (Exception e) {
                auditLog.error("Error inside @method generateDraft", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), GENERATE_DRAFT, userContextHolder.getCurrentUser().getId(), auditMap.get("subject"), auditMap.get("conversationId"), null, null, convertToJSON(auditMap, true), null);
            }
            return obj;
        } catch (Exception e) {
            log.error("Error inside @method generateDraftV1", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.EMAIL_IS_NULL);
        }
    }

    @PostMapping("/generateDraftFromContent")
  /*  @Operation(summary = "Generate reply draft using content of email", tags = "Draft", description = "Generate a reply draft based on the provided email content when summary of the email is not generated.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft generated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })*/
    public Map<String, String> generateDraftFromContent(@RequestBody DraftWrapper emailObject) {
        UserInfo userInfo = userContextHolder.getCurrentUser();
        changeDraftWrapper(emailObject);
        Map<String, String> obj = new HashMap<>();
        try {
            String objective = emailObject.getObjective();
            String content = emailObject.getContent();

            Map<String, String> auditMap = CommonUtils.getAuditMap(userInfo.getId(), "", "", GENERATE_DRAFT, objective, null, "", "");
            try {
                DraftResponse draftMessage;
                String previousDraft = emailObject.getPreviousDraft();
                if (previousDraft == null || previousDraft.isBlank()) {
                    draftMessage = draftServiceAI.generateDraftFromContent(objective, content, emailObject.getRecipients(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), null, auditMap);
                } else {
                    draftMessage = draftServiceAI.reGenerateDraft(objective, content, emailObject.getRecipients(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), previousDraft, auditMap);
                }
                obj.put(EmailConstants.RESULT, convertToJSON(draftMessage, true));

            } catch (Exception e) {
                auditLog.error("Error inside @method generateDraftFromContent", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), "GENERATE_DRAFT_FROM_CONTENT", userContextHolder.getCurrentUser().getId(), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
            }
            return obj;
        } catch (Exception e) {
            log.error("Error inside @method generateDraftV1", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.EMAIL_IS_NULL);
        }
    }

//    /v1/improveDraft
    @PostMapping("/improveDraft")
    /*@Operation(summary = "Improve email draft", tags = "Draft", description = "Improve the content of an existing reply draft based on user provided input.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft improved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })*/
    public Map<String, String> improveDraft(@RequestBody DraftWrapper emailObject) {
        try {
            UserInfo userInfo = userContextHolder.getCurrentUser();
            Map<String, String> obj = new HashMap<>();
            String userPrompt = emailObject.getUserPrompt();
            String internetMessageId = emailObject.getInternetMessageId();
            String draft = emailObject.getDraft();
            MailSummary message = mailSummaryDao.findByInternetMessageId(internetMessageId, userInfo.getId());
            Map<String, String> auditMap = CommonUtils.getAuditMap(userInfo.getId(), message.getSubject(), message.getInternetMessageId(), IMPROVE_DRAFT, userPrompt, null, message.getMessageId(), message.getConversationId());

            try {
                DraftResponse draftMessage = draftServiceAI.improveDraft(draft, userPrompt, message.getFromUser(), userInfo.getEmail(), emailObject.getLength(), emailObject.getTone(), null, auditMap);

                obj.put(EmailConstants.RESULT, convertToJSON(draftMessage, true));
            } catch (Exception e) {
                auditLog.error("Error inside @method improveDraft", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), IMPROVE_DRAFT, userContextHolder.getCurrentUser().getId(), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
            }
            return obj;
        } catch (Exception e) {
            log.error("Error inside @method improveDraft", e);
            return Map.of(EmailConstants.RESULT, EmailConstants.EMAIL_IS_NULL);
        }
    }

//    /improveSelection
    @PostMapping("/improveSelection")
  /*  @Operation(summary = "Improve selected email content", tags = "Draft", description = "Improve the content of a selected portion of an existing draft email based on the provided email object.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Selected content improved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })*/
    public Map<String, String> improveSelection(@RequestBody DraftWrapper emailObject) {
        changeDraftWrapper(emailObject);
        String userPrompt = emailObject.getUserPrompt();
        if (userPrompt == null) userPrompt = "";

        String selection = emailObject.getSelection();
        String draft = emailObject.getDraft();
        Map<String, String> auditMap = CommonUtils.getAuditMap(userContextHolder.getCurrentUser().getEmail(), null, null, IMPROVE_SELECTION, selection, userPrompt, null, null);
        Map<String, String> obj = new HashMap<>();

        try {
            DraftResponse draftMessage = draftServiceAI.improveSelection(draft, selection, userPrompt, emailObject.getLength(), emailObject.getTone(), auditMap);
            obj.put(EmailConstants.RESULT, convertToJSON(draftMessage, true));
        } catch (Exception e) {
            auditLog.error("Error inside @method improveSelection", e.getMessage(), getStackTraceAsString(e), auditMap.get("messageId"), auditMap.get("internetMessageId"), IMPROVE_SELECTION, userContextHolder.getCurrentUser().getId(), auditMap.get("subject"), auditMap.get("conversationId"), null, null, AIUtils.convertToJSON(auditMap, true), null);
        }
        return obj;
    }

//    /v1/getFreshDraft
    @PostMapping("/getFreshDraft")
    /*@Operation(summary = "Get fresh draft email", tags = "Draft", description = "Generate a fresh draft email based on the provided request data.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Fresh draft generated successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })*/
    public FreshDraftAIResponse getFreshDraft(@RequestBody FreshDraftRequest request) {
        log.error("Inside method get Fresh Draft {}", request.getIntent());
        try {
            return draftServiceAI.getFreshDraft(request.getIntent(), request.getUser(), request.getLength(), request.getTone());
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("userPrompt", request.getIntent(), "previousPrompt", null, "tone", request.getTone(), "length", request.getLength());
            auditLog.error("Error inside @method fresh draft", e.getMessage(), getStackTraceAsString(e), null, null, FRESH_DRAFT, userContextHolder.getCurrentUser().getId(), null, null, null, null, AIUtils.convertToJSON(auditMap, true), null);
            log.error("Error getting fresh draft", e);
        }
        return null;
    }

    @PostMapping("/improveFreshDraft")
    /*@Operation(summary = "Improve fresh draft email", tags = "Draft", description = "Improve a fresh draft email based on the provided user input and draft details.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Draft improved successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    })*/
    public FreshDraftAIResponse improveFreshDraft(@RequestBody FreshDraftRequest request) {
        String user = request.getUser();
        String draft = request.getDraft();
        String intent = request.getIntent();
        String length = request.getLength();
        String tone = request.getTone();

        try {
            return draftServiceAI.improveFreshDraft(draft, intent, user, length, tone);
        } catch (Exception e) {
            Map<String, String> auditMap = Map.of("userPrompt", intent, "previousPrompt", draft, "tone", tone, "length", length, "user", user);
            auditLog.error("Error inside @method improveFreshDraft", e.getMessage(), getStackTraceAsString(e), null, null, IMPROVE_FRESH_DRAFT, userContextHolder.getCurrentUser().getId(), null, null, null, null,
                    AIUtils.convertToJSON(auditMap, true), null);
        }
        return null;
    }

    private void changeDraftWrapper(DraftWrapper draftWrapper) {
        String messageId = draftWrapper.getMessageId();
        if (messageId == null) return;

        if (pollingMode.equalsIgnoreCase("EWS")) {
            messageId = messageId.replace(" ", "+");
        } else {
            messageId = messageId.replace("/", "-");
        }
        draftWrapper.setMessageId(messageId);
    }

}

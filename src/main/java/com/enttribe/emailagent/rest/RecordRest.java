package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.dto.UserFoldersRecord;
import com.enttribe.emailagent.entity.MailSummary;
import com.enttribe.emailagent.entity.UserActions;
import com.enttribe.emailagent.entity.UserFolders;
import com.enttribe.emailagent.repository.IMailSummaryDao;
import com.enttribe.emailagent.repository.UserActionsDao;
import com.enttribe.emailagent.service.EwsService;
import com.enttribe.emailagent.service.GraphIntegrationService;
import com.enttribe.emailagent.service.NotificationService;
import com.enttribe.emailagent.service.RecordService;
import com.enttribe.emailagent.service.UserFoldersService;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.utils.EmailConstants;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * The type Record rest.
 *  <AUTHOR> Dangi
 */
@RestController("recordRest")
@Slf4j
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/emailservice")
public class RecordRest {

    @Autowired
    private RecordService recordService;

    @Autowired(required = false)
    private NotificationService notificationService;

    @Autowired
    private EwsService ewsService;

    @Autowired
    private GraphIntegrationService graphIntegrationService;

    @Autowired
    private UserContextHolder userContextHolder;

    @Autowired
    private UserFoldersService userFoldersService;

    @Autowired
    private IMailSummaryDao mailSummaryDao;

    @Value("${pollingMode}")
    private String pollingMode;

    @Autowired
    private UserActionsDao userActionsDao;

    //used
    @PostMapping(path = "/validateUser")
    /* @Operation(summary = "Validate User by Email and Secret", tags = "User", description = "Validates the user by the provided email and secret (password or token).")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User successfully validated."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while validating the user.")
    }) */
    public Map<String, Object> validateUser(@RequestBody Map<String, String> request) {
        return recordService.validateUserByEmailAndPhone(request.get("email"), request.get("secret"));
    }


    @PostMapping(path = "/sendNotification")
    /* @Operation(summary = "Send Notification", tags = "Notification", description = "Sends an FCM (Firebase Cloud Messaging) notification to the specified device.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Notification successfully sent."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while sending the notification.")
    }) */
    public String sendNotification(@RequestBody Map<String, String> request) {
        MailSummary mailSummary = mailSummaryDao.findById(Integer.parseInt(request.get("id"))).orElse(new MailSummary());
        return notificationService.sendFCMNotification(request.get("deviceId"), mailSummary);
    }

    @PostMapping(path = "/introspectEwsToken")
    /* @Operation(summary = "Introspect EWS Token", tags = "Authentication", description = "Introspects the provided EWS token and checks its validity.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Token introspection successful, token is valid."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while introspecting the token.")
    }) */
    public ResponseEntity<Map<String, Boolean>> introspectEwsToken(@RequestHeader("ews-token") String token) {
        boolean result = recordService.introspectEwsToken(token);
        if (result) {
            return ResponseEntity.status(HttpStatus.OK).body(Map.of(EmailConstants.RESULT, true));
        } else {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of(EmailConstants.RESULT, false));
        }
    }

    @GetMapping("/getQuestionsByIntentName")
    /* @Operation(summary = "Get Questions by Intent Name", tags = "Intent", description = "Fetches a list of questions based on the provided intent name.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Questions successfully fetched based on the intent name."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the questions.")
    }) */
    public String getQuestionsByIntentName(@RequestParam String intentName, @RequestHeader("Authorization") String token) {
        return recordService.getQuestionsByIntentName(intentName, token);
    }

    @GetMapping("/getChartJson")
    /* @Operation(summary = "Get Chart JSON", tags = "Chart", description = "Fetches the JSON data for a chart based on the provided question.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Chart JSON successfully fetched based on the question."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the chart JSON.")
    }) */
    public Object getChartJson(@RequestParam("question") String question, @RequestHeader("Authorization") String token) {
        return recordService.getChartJson(question, token);
    }

    @PostMapping(path = "/markActionTaken")
    /* @Operation(summary = "Mark action taken", tags = "Action", description = "Mark an action as taken based on the provided request body.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Action marked successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    }) */
    public Map<String, String> markActionTaken(@RequestBody Map<String, Object> jsonBody) {
        try {
            String userId = userContextHolder.getCurrentUser().getId();
            String internetMessageId = (String) jsonBody.get("internetMessageId");
            Boolean action = (Boolean) jsonBody.get("action");
            log.debug("Inside @method markActionTaken. @param : userId -> {} internetMessageId -> {}", userId, internetMessageId);

            int actionTaken = userActionsDao.markActionTaken(userId, internetMessageId, action);
            log.debug("Action taken on {} user actions", actionTaken);

            return Map.of("result", "success");
        } catch (Exception e) {
            log.error("Error inside @method markActionTaken", e);
            return Map.of("result", "failed");
        }
    }

    @PostMapping(path = "/markAllActionTaken")
    /* @Operation(summary = "Mark action taken", tags = "Action", description = "Mark an action as taken based on the provided request body.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Action marked successfully."),
            @ApiResponse(responseCode = "500", description = "Internal server error.")
    }) */
    public Map<String, String> markAllActionTaken(@RequestBody List<String> internetMessageIds) {
        try {
            String userId = userContextHolder.getCurrentUser().getId();
            log.debug("Inside @method markAllActionTaken. @param : userId -> {}, internetMessageIds -> {}", userId,internetMessageIds.toString());

            int actionTaken = userActionsDao.markAllActionTaken(userId);

            log.debug("Action taken on {} emails", actionTaken);
            return Map.of("result", "success");
        } catch (Exception e) {
            log.error("Error inside @method markAllActionTaken", e);
            return Map.of("result", "failed");
        }
    }

    //used
    @PostMapping(path = "/getEmailsByTag")
    /* @Operation(summary = "Get Emails by Tag", tags = "Email", description = "Fetches a list of emails associated with the provided tags.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Emails successfully fetched based on the tags."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the emails.")
    }) */
    public List<MailSummary> getEmailsByTag(@RequestBody(required = false) List<String> tags) {
        String userId = userContextHolder.getCurrentUser().getId();
        return recordService.getEmailsByTag(tags, userId);
    }

    //could be used
    @PostMapping(path = "/v1/getEmailsByTag")
    /* @Operation(summary = "Get Emails by Tag (v1)", tags = "Email", description = "Fetches a list of emails associated with the provided tags (version 1 of the API).")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Emails successfully fetched based on the tags."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the emails.")
    }) */
    public List<MailSummary> getEmailsByTagV1(@RequestBody(required = false) List<String> tags) throws Exception {
        String email = userContextHolder.getCurrentUser().getEmail();
        return recordService.getEmailsByTag(tags, email);
    }

    @PostMapping(path = "/tagEmail")
    /* @Operation(summary = "Tag Email", tags = "Email", description = "Tags an email with a specified tag, based on the provided message ID and tag name.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Email successfully tagged with the specified tag."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while tagging the email.")
    }) */
    public Map<String, Object> tagEmail(@RequestBody Map<String, String> request) throws Exception {
        String email = userContextHolder.getCurrentUser().getEmail();
//        String email = request.get("email");
        boolean result;
        if (pollingMode.equalsIgnoreCase("EWS")) {
            result = ewsService.addCustomTags(email, request.get("messageId"), request.get("tag"));
        } else {
            result = graphIntegrationService.tagEmail(email, request.get("messageId"), request.get("tag"), true);
        }
        return Map.of("result", result);
    }


    //used
    @GetMapping(path = "/getUsersMailFolders")
    /* @Operation(summary = "Get Users Mail Folders", tags = "Mail", description = "Fetches the list of mail folders for the current user.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Mail folders successfully fetched for the user."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the mail folders.")
    }) */
    public List<UserFolders> getUsersMailFolders() {
        return userFoldersService.getUsersMailFolders();
    }


    //used
    @PostMapping(path = "/updateFolderStatus")
    /* @Operation(summary = "Update Folder Status", tags = "Mail", description = "Updates the status of specified mail folders.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Folder status successfully updated."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the folder status.")
    }) */
    public Map<String, String> updateFolderStatus(@RequestBody List<UserFoldersRecord> foldersRecords) {
        boolean result = userFoldersService.updateFolderStatus(foldersRecords);
        return result ? Map.of(EmailConstants.RESULT, "success") : Map.of(EmailConstants.RESULT, "failed");
    }


    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "/userActionsByFilter")
    /* @Operation(summary = "Get User Actions by Filter", tags = "User Actions", description = "Fetches a list of user actions based on the provided filters, with optional limits on the number of records.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "User actions successfully fetched based on the filters."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching user actions.")
    }) */
    public List<UserActions> userActionsByFilter(
            @RequestParam(required = false, name = "llimit") Integer llimit,
            @RequestParam(required = false, name = "ulimit") Integer ulimit,
            @RequestBody Map<String, Object> filterMap) {
        return recordService.userActionsByFilter(llimit, ulimit, filterMap);
    }

     @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "/userActionsCountByFilter")
     /* @Operation(summary = "Get User Actions Count by Filter", tags = "User Actions", description = "Fetches the count of user actions based on the provided filters.")
     @ApiResponses(value = {
             @ApiResponse(responseCode = "200", description = "User actions count successfully fetched based on the filters."),
             @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the user actions count.")
     }) */
     public Long userActionsCountByFilter(@RequestBody Map<String, Object> filterMap) {
         return recordService.userActionsCountByFilter(filterMap);
     }

    @PostMapping(path = "/filterEmails")
    /* @Operation(summary = "Filter Emails", tags = "Email", description = "Fetches a list of emails based on provided filters with pagination.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Filtered emails successfully fetched."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the emails.")
    }) */
    public List<MailSummary> filterEmails(
            @RequestParam(value = "lowerLimit", required = false) Integer lowerLimit,
            @RequestParam(value = "upperLimit", required = false) Integer upperLimit,
            @RequestBody(required = false) Map<String, Object> filters) throws Exception {

        String email = userContextHolder.getCurrentUser().getEmail();
        return recordService.filterEmails(filters, email, lowerLimit, upperLimit);
    }

}

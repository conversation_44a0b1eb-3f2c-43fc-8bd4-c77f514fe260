package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.ai.polling.OutlookPollingAI;
import com.enttribe.emailagent.dto.EventDto;
import com.enttribe.emailagent.dto.Meeting;
import com.enttribe.emailagent.dto.UserEmailResponseDto;
import com.enttribe.emailagent.entity.UserMailAttachment;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.integration.GmailIntegration;
import com.enttribe.emailagent.utils.KafkaConsumer;
import com.enttribe.emailagent.wrapper.MessageWrapper;
import com.google.api.services.calendar.model.Event;
import com.google.api.services.calendar.model.Events;
import com.google.api.services.gmail.model.ListMessagesResponse;
import com.google.api.services.gmail.model.Message;
import com.google.api.services.gmail.model.VacationSettings;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * The type Gmail rest controller.
 *  <AUTHOR> Dangi
 */
@RestController("gmailRestController")
@RequestMapping(path = "/gmailRestController")
//@Slf4j
public class GmailRestController {
    private static final Logger log = EmailAgentLogger.getLogger(GmailRestController.class);

    @Autowired
    private GmailIntegration gmailIntegration;

    @Autowired
    private KafkaConsumer kafkaConsumer;

    @Autowired
    private  OutlookPollingAI polling;

    @GetMapping("/summarizeMailAttachment")
  /*  @Operation(summary = "Summarize Mail Attachments", tags = "Mail Attachments", description = "Summarizes mail attachments based on the provided conversation ID, email ID, and message ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully summarized the mail attachments."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while summarizing mail attachments.")
    })*/
    public List<UserMailAttachment> summarizeMailAttachment(@RequestParam("conversationId") String conversationId, @RequestParam("emailId") String emailId, @RequestParam("messageId") String messageId){
        log.info("inside summarizeMailAttachment");
        return gmailIntegration.summarizeMailAttachment(conversationId,emailId,messageId);
    }

    @GetMapping("/pollGmailFolderWise")
  /*  @Operation(summary = "Poll Gmail Folder Wise", tags = "Gmail Integration", description = "Polls a Gmail account's folder based on the provided user email, time, and folder ID, and processes the results.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully polled Gmail folder and returned the response."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while polling the Gmail folder.")
    })*/
    public UserEmailResponseDto pollGmailFolderWise(@RequestParam("userEmail") String userEmail, @RequestParam("time") String time, @RequestParam("folderId") String folderId){
        log.info("inside pollGmailFolderWise");
        UserEmailResponseDto userEmailResponseDto=gmailIntegration.pollGmailFolderWise(userEmail,time,folderId);
        polling.consumeKafka(userEmail,userEmail,userEmailResponseDto.getMessages(),folderId);
        return userEmailResponseDto;
    }

    @GetMapping("/getLogs")
   /* @Operation(summary = "Retrieve Gmail Logs", tags = "Gmail Integration", description = "Retrieves the logs related to Gmail integration.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the Gmail logs."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the Gmail logs.")
    })*/
    public String getLogs(){
        return gmailIntegration.getLogs();
    }


    @GetMapping("/getMessageById")
   /* @Operation(summary = "Retrieve Gmail Message by ID", tags = "Gmail Integration", description = "Retrieves a Gmail message based on the provided user email and message ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the Gmail message."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the Gmail message.")
    })*/
    public Message getMessageById(@RequestParam("userEmail") String userEmail, @RequestParam("message") String message){
        log.info("inside pollGmailFolderWise");
//        gmailIntegration.getCalendarEventsByMessageId(userEmail,message);
        return gmailIntegration.getMessageById(userEmail,message);
    }


    @GetMapping("/getMessages")
  /*  @Operation(summary = "Retrieve Gmail Messages", tags = "Gmail Integration", description = "Retrieves a list of Gmail messages based on the provided user email.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the list of Gmail messages."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the Gmail messages.")
    })*/
    public ListMessagesResponse getMessages(@RequestParam("userEmail") String userEmail){
        log.info("inside pollGmailFolderWise");
        return gmailIntegration.getMessages(userEmail);
    }
    @GetMapping("/fetchAttachmentBtId")
    public EventDto fetchAttachmentBtId(@RequestParam("userEmail") String userEmail, @RequestParam("message") String message, @RequestParam("event") String event){
        log.info("inside getEvent");
        return gmailIntegration.fetchAttachmentBtId(userEmail,message,event);
    }

    @GetMapping("/getEvent")
    public Events getEvent(@RequestParam("userEmail") String userEmail){
        log.info("inside getEvent");
        return gmailIntegration.getEvent(userEmail);
    }
    @GetMapping("/getEventById")
   /* @Operation(summary = "Retrieve Gmail Event by Message ID", tags = "Gmail Integration", description = "Retrieves a Gmail event by the provided user email and message ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the Gmail event."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the Gmail event.")
    })*/
    public Event getEventById(@RequestParam("userEmail") String userEmail,@RequestParam("messageId") String messageId){
        log.info("inside getEventmessageId");
        return gmailIntegration.getEventById(userEmail,messageId);
    }

    @GetMapping("/getLabels")
 /*   @Operation(summary = "Retrieve Gmail Labels", tags = "Gmail Integration", description = "Retrieves a list of labels (mail folders) for the provided user email.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the Gmail labels."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the Gmail labels.")
    })*/
    public   List<Map<String, String>> getLabels(@RequestParam("userEmail") String userEmail) {
        log.info("inside getEvent");
        return gmailIntegration.usersMailFolders(userEmail);
    }

    @GetMapping("/getAvailableMeetingSlots")
   /* @Operation(summary = "Retrieve Available Meeting Slots", tags = "Gmail Integration", description = "Retrieves a list of available meeting slots based on the provided user emails, start date/time, end date/time, and slot duration.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved available meeting slots."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving available meeting slots.")
    })*/
    public    List<Meeting> getAvailableMeetingSlots(@RequestParam("userEmails") List<String> userEmail, @RequestParam("startDateTime") String startDateTime, @RequestParam("endDateTime") String endDateTime, @RequestParam("slotDuration") Integer slotDuration){
        log.info("inside getAvailableMeetingSlots");
        return gmailIntegration.getAvailableMeetingSlots(userEmail,startDateTime,endDateTime,slotDuration);
    }

    @GetMapping("/getAllScheduledMeetings")
  /*  @Operation(summary = "Retrieve All Scheduled Meetings", tags = "Gmail Integration", description = "Retrieves a list of all scheduled meetings based on the provided user email, start date/time, and end date/time.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved all scheduled meetings."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving scheduled meetings.")
    })*/
    public    List<Event> getAllScheduledMeetings(@RequestParam("userEmails") String userEmail, @RequestParam("startDateTime") String startDateTime, @RequestParam("endDateTime") String endDateTime){
        log.info("inside getAvailableMeetingSlots");
        return gmailIntegration.getAllScheduledMeetings(userEmail,startDateTime,endDateTime);
    }

    @GetMapping("/consumeEmailMessages")
  /*  @Operation(summary = "Consume Email Messages", tags = "Kafka Integration", description = "Consumes email messages from Kafka.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully consumed the email messages."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while consuming email messages.")
    })*/
    public String consumeEmailMessages(){
        kafkaConsumer.consumeEmailMessages();
        return "yes";
    }


    @GetMapping("/getVacationResponderSettings")
/*    @Operation(summary = "Retrieve Vacation Responder Settings", tags = "Gmail Integration", description = "Retrieves the vacation responder settings for the specified user email.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the vacation responder settings."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving vacation responder settings.")
    })*/
    public VacationSettings getVacationResponderSettings(@RequestParam("userEmails") String userEmail){
        log.info("inside getAvailableMeetingSlots");
        return gmailIntegration.getVacationResponderSettings(userEmail);
    }


    @PostMapping("/setVacationResponder")
   /* @Operation(summary = "Set Vacation Responder", tags = "Gmail Integration", description = "Sets the vacation responder for the specified user email with the provided vacation settings.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully set the vacation responder."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while setting the vacation responder.")
    })*/
    public String setVacationResponder(@RequestParam("userEmails") String userEmails, @RequestBody() VacationSettings vacationSettings){
        log.info("inside summarizeMailAttachment");
        return gmailIntegration.setVacationResponder(userEmails,vacationSettings);
    }



    @PostMapping("/createMeetingEvent")
  /*  @Operation(summary = "Create a New Meeting Event", tags = "Gmail Integration", description = "Creates a new meeting event for the specified user ID with the provided event details.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully created the meeting event."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while creating the meeting event.")
    })*/
    public    String createMeetingEvent(@RequestParam("userId") String userId, @RequestBody() Map<String, Object> requestBody){
        log.info("inside createMeetingEvent");
        gmailIntegration.createMeetingEvent(userId,requestBody);
        return null;
    }


    @GetMapping("/getMessageDetails")
  /*  @Operation(summary = "Get Message Details", tags = "Gmail Integration", description = "Fetches the detailed information of a specific message by its message ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the message details."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching message details.")
    })*/
    public MessageWrapper getMessageDetails(@RequestParam("messageId") String messageId){
        log.info("inside createMeetingEvent");
        return  gmailIntegration.getMessageWrapperById(messageId);
    }

    @PostMapping(value = "/eventcomes", produces = MediaType.TEXT_PLAIN_VALUE)
   /* @Operation(summary = "Handle Event Notification", tags = "Event Notifications", description = "Handles the incoming event notification payload and validation token. If a validation token is provided, it is returned. Otherwise, no content is returned.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully handled the notification."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while processing the notification.")
    })*/
    public ResponseEntity<String> handleNotification(
            @RequestParam(value = "validationToken", required = false) String validationToken,
            @RequestBody(required = false) String notificationPayload) {

        if (notificationPayload != null) {
            log.info("Received notification payload:--------- {}", notificationPayload);

        } else {
            log.warn("No notification payload received.");
        }
        if (validationToken != null) {
            log.info("Received validationToken: {}", validationToken);
            return ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN).body(validationToken);
        }

        return ResponseEntity.ok().build();
    }

}

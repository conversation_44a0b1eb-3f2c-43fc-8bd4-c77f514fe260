package com.enttribe.emailagent.rest;

import com.enttribe.emailagent.dto.EmailUserDto;
import com.enttribe.emailagent.entity.EmailUser;
import com.enttribe.emailagent.exception.EmailAgentLogger;
import com.enttribe.emailagent.service.EmailUserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;



/**
 * REST controller for managing EmailUser entities.
 *
 * Provides endpoints for CRUD operations on EmailUser entities,
 * as well as a search endpoint for querying EmailUser entities
 * based on various criteria.
 *  <AUTHOR>
 */
@RestController("emailUserRestController")
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/emailUserRestController")
public class EmailUserRestController {

    private static final Logger log = EmailAgentLogger.getLogger(EmailUserRestController.class);

    @Autowired
    private EmailUserService emailUserService;

    /**
     * Retrieves all EmailUser entities.
     *
     * @return a list of EmailUser objects.
     */
    @GetMapping
   /* @Operation(summary = "Retrieve all Email Users", tags = "Email Users", description = "Fetches all EmailUser entities stored in the system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved all email users."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving email users.")
    })*/
    public List<EmailUser> getAllEmailUsers() {
        return emailUserService.findAll();
    }

    /**
     * Retrieves an EmailUser entity by its ID.
     *
     * @param id the ID of the EmailUser to retrieve.
     * @return the EmailUser object.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Retrieve Email User by ID", tags = "Email Users", description = "Fetches a single EmailUser entity based on the provided ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the email user by ID."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving the email user.")
    })
    public EmailUser getEmailUserById(@PathVariable Integer id) {
        return emailUserService.findById(id);
    }

    /**
     * Creates a new EmailUser entity.
     *
     * @param emailUser the EmailUser object to create.
     * @return the created EmailUser object.
     */
    @PostMapping("createEmailUser")
   /* @Operation(summary = "Create new Email User", tags = "Email Users", description = "Creates a new EmailUser entity based on the provided data.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully created a new email user."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while creating the email user.")
    })*/
    public EmailUser createEmailUser(@RequestBody EmailUser emailUser) {
        return emailUserService.save(emailUser);
    }

    @PostMapping("saveEmailUser")
   /* @Operation(summary = "Create new Email User v1", tags = "Email Users", description = "Creates a new EmailUser entity based on the provided data.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully created a new email user."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while creating the email user.")
    })*/
    public EmailUser saveEmailUser(@RequestBody EmailUserDto emailUser) {
        return emailUserService.saveEmailUser(emailUser);
    }

    @PostMapping("/userEnableOrDisable")
  /*  @Operation(summary = "Enable or Disable Email User", tags = "Email Users", description = "Enables or disables an EmailUser entity based on email.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully enabled or disabled the email user."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while enabling or disabling the email user.")
    })*/
    public Map<String, String> updateUserStatus(@RequestBody Map<String, String> emailUser) {
        String email = emailUser.get("email");
        String status = emailUser.get("status");
        return emailUserService.updateUserStatus(email, status);
    }

    @PostMapping("/createBulkEmailUser")
  /*  @Operation(summary = "Bulk create Email Users", tags = "Email Users", description = "Creates multiple EmailUser entities from a CSV file.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully created multiple email users from the CSV file."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while processing the CSV file.")
    })*/
    public List<Map<String, String>> createUsersFromCsv(@RequestParam("file") MultipartFile file) throws IOException {
        return emailUserService.createUsersFromCsv(file);
    }


    /**
     * Updates an existing EmailUser entity.
     *
     * @param id the ID of the EmailUser to update.
     * @param emailUserDetails the updated EmailUser object.
     * @return the updated EmailUser object.
     */
    @PostMapping("updateEmailUser/{id}")
/*    @Operation(summary = "Update an existing Email User", tags = "Email Users", description = "Updates an existing EmailUser entity with the provided details.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated the EmailUser entity."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the EmailUser.")
    })*/
    public EmailUserDto updateEmailUser(@PathVariable Integer id, @RequestBody EmailUserDto emailUserDetails) {
        return emailUserService.update(id, emailUserDetails);
    }



    /**
     * Updates an existing EmailUser entity.
     *
     * @param id the ID of the EmailUser to update.
     * @param deleted the updated status string.
     * @return the updated EmailUser object.
     */
    @PostMapping("updateStatus/{id}")
  /*  @Operation(summary = "Update the status of an Email User", tags = "Email Users", description = "Updates the 'deleted' status of an EmailUser entity.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated the EmailUser status."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while updating the EmailUser status.")
    })*/
    public EmailUser updateStatus(@PathVariable Integer id, @RequestParam boolean deleted) {
        return emailUserService.updateStatus(id, deleted);
    }

    /**
     * Deletes an EmailUser entity by its ID.
     *
     * @param id the ID of the EmailUser to delete.
     * @return true if the deletion was successful, false otherwise.
     */
    @GetMapping("delete/{id}")
  /*  @Operation(summary = "Delete an Email User", tags = "Email Users", description = "Deletes an EmailUser entity by its ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully deleted the EmailUser."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while deleting the EmailUser.")
    })*/
    public Boolean deleteEmailUser(@PathVariable Integer id) {
        return emailUserService.deleteById(id);
    }



    @GetMapping("getDistinctBatchId")
   /* @Operation(summary = "Get Distinct Batch Ids", tags = "Email Users", description = "Retrieves a list of distinct batch IDs, optionally filtered by the provided batchId.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved distinct batch IDs."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving distinct batch IDs.")
    })*/
    public List<String> getDistinctBatchId(@RequestParam(required = false, name = "batchId") String batchId) {
        return emailUserService.getDistinctBatchId(batchId);
    }

    @GetMapping("getBatchId")
  /*  @Operation(summary = "Get All Batch Ids", tags = "Email Users", description = "Retrieves a list of all batch IDs."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved all batch IDs."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while retrieving batch IDs.")
    })*/
    public List<String> getBatchId() {
        return emailUserService.getBatchId();
    }

    /**
     * Searches for EmailUser entities based on the provided criteria.
     *
     * @param id the ID of the EmailUser (optional).
     * @param userId the userId of the EmailUser (optional).
     * @param email the email of the EmailUser (optional).
     * @param type the type of the EmailUser (optional).
     * @param deleted the deleted status of the EmailUser (optional).
     * @param name the name of the EmailUser (optional).
     * @return a list of EmailUser objects that match the search criteria.
     */
    @GetMapping("/search")
  /*  @Operation(summary = "Search Email Users", tags = "Email Users", description = "Searches for EmailUser entities based on the provided criteria.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the search results."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while searching EmailUsers.")
    })*/
    public List<EmailUser> search(@RequestParam(name = "id", required = false) Integer id,
                                  @RequestParam(name = "userId", required = false) String userId,
                                  @RequestParam(name = "email", required = false) String email,
                                  @RequestParam(name = "type", required = false) String type,
                                  @RequestParam(name = "deleted", required = false) Boolean deleted,
                                  @RequestParam(name = "name", required = false) String name,
                                  @RequestParam(name = "lLimit", defaultValue = "0") int lLimit,
                                  @RequestParam(name = "uLimit", defaultValue = "25") int uLimit) {
        log.info("Fetching all search logs");
        return emailUserService.search(id, userId, email, type, deleted, name,lLimit,uLimit);
    }


    /**
     * Searches for EmailUser entities based on the provided criteria.
     *
     * @param id the ID of the EmailUser (optional).
     * @param userId the userId of the EmailUser (optional).
     * @param email the email of the EmailUser (optional).
     * @param type the type of the EmailUser (optional).
     * @param deleted the deleted status of the EmailUser (optional).
     * @param name the name of the EmailUser (optional).
     * @return a list of EmailUser objects that match the search criteria.
     */
    @GetMapping("/count")
    /*@Operation(summary = "Count Email Users", tags = "Email Users", description = "Counts the number of EmailUser entities based on the provided search criteria.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the count of EmailUsers."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while counting EmailUsers.")
    })*/
    public Long count(@RequestParam(name = "id", required = false) Integer id,
                                  @RequestParam(name = "userId", required = false) String userId,
                                  @RequestParam(name = "email", required = false) String email,
                                  @RequestParam(name = "type", required = false) String type,
                                  @RequestParam(name = "deleted", required = false) Boolean deleted,
                                  @RequestParam(name = "name", required = false) String name,
                                  @RequestParam(name = "lLimit", defaultValue = "0") int lLimit,
                                  @RequestParam(name = "uLimit", defaultValue = "25") int uLimit
    ) {
        log.info("Fetching all count logs");
        return emailUserService.count(id, userId, email, type, deleted, name,lLimit,uLimit);
    }



    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "emailUserByFilter")
  /*  @Operation(summary = "Fetch Email Users by Filter", tags = "Email Users", description = "Fetches a list of EmailUser entities based on the provided filter criteria.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the filtered list of EmailUsers."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching filtered EmailUsers.")
    })*/
    List<EmailUserDto> emailUserByFilter(@Parameter(name = "Minimum number of records required")
                                         @RequestParam(required = false, name = "llimit") Integer llimit,
                                         @Parameter(name = "Maximum number of records required")
                                         @RequestParam(required = false, name = "ulimit") Integer ulimit,
                                         @Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap){
        log.info("Fetching emailUserByFilter logs");
        return emailUserService.emailUserByFilter(llimit, ulimit, filterMap);
    }

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "emailUserCountByFilter")
  /*  @Operation(summary = "Fetch Email User Count by Filter", tags = "Email Users", description = "Retrieves the count of EmailUser entities based on the provided filter criteria.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the count of EmailUsers matching the filter."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while fetching the count of EmailUsers.")
    })*/
    long emailUserCountByFilter(@Parameter(name = "filtermap") @RequestBody Map<String, Object> filterMap){
        return emailUserService.emailUserCountByFilter(filterMap);
    }


    @GetMapping("/saveContactUsers")
 /*   @Operation(summary = "Save Contact Users", tags = "Email Users", description = "Saves contact information for users in the system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully saved contacts for users."),
            @ApiResponse(responseCode = "500", description = "Internal server error occurred while saving contacts.")
    })*/
    public String saveContacts() {
        emailUserService.saveContactsForUsers();
        return "Contacts saved successfully!";
    }


}
